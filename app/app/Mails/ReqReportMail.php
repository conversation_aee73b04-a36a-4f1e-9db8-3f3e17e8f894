<?php

namespace App\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ReqReportMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * 새로운 메시지 인스턴스를 생성
     *
     * @param string $fileData base64로 인코딩된 첨부파일 데이터
     * @param string $filename 첨부파일명
     * @param string $title 메일 제목
     * @param string $content 메일 내용
     */

    public function __construct(
        public string $fileData,
        public string $filename,
        public string $title,
        public string $content,
    ) {
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->title,
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'emails.req-report',
            with: [
                'title' => $this->title,
                'content' => $this->content,
            ],
        );
    }

    public function attachments(): array
    {
        return [
            // 큐에서 전달받은 base64 인코딩된 데이터를 다시 바이너리로 디코딩하여 첨부합니다.
            Attachment::fromData(fn () => base64_decode($this->fileData), $this->filename)
                ->as($this->filename)
                ->withMime('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        ];
    }
}
